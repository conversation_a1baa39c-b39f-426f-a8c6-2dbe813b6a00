import json
import requests
import time
import sys
import os
from datetime import date
from typing import Dict, Any
import copy

# 添加项目根路径到系统路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from steps.tools import get_prompt, qwen72B_sli,qwen3


class StatisticsDemo:    
    def __init__(self):
        self.api_url = 'http://192.168.219.106:8086/analyzeQuestion'
        self.headers = {
            'Content-Type': 'application/json'
        }
    
    def call_analyze_question_api(self, question: str) -> Dict[str, Any]:
        params = {
            "params": {
                "question": question
            }
        }
       
        response = requests.post(
            url=self.api_url,
            data=json.dumps(params),
            headers=self.headers,
            verify=False,
            timeout=30
        )
        response.raise_for_status()
        result = response.json()
        return result
    
    def get_mql_from_metrics(self,question: str) -> Dict[str, Any]:
        try:
            api_result = self.call_analyze_question_api(question)
            custom_data = api_result.get('custom', {})

            simplified_data = copy.deepcopy(custom_data)
            if 'metrics' in simplified_data:
                for metric in simplified_data['metrics']:
                    simplified_metric = {
                        'name': metric.get('name', ''),
                        'description': metric.get('description', ''),
                        'category': metric.get('category', '')
                    }
                    metric.clear()
                    metric.update(simplified_metric)

            simplified_str = json.dumps(simplified_data, ensure_ascii=False, indent=2)
            print(simplified_str)

            metrics_str = json.dumps(custom_data, ensure_ascii=False, indent=2)
            messages = [
                {
                    "role": "user",
                    "content": get_prompt('MQL.md', question, '', '', '', metrics_str, str(date.today()))
                }
            ]
            print(messages)
            response = qwen3(messages)
            jsonstr = response.replace("```", "").replace("json", "")
            mql_result = json.loads(jsonstr)
            print(json.dumps(mql_result, indent=4, ensure_ascii=False))
            return mql_result
        except Exception as e:
            print(f"MQL生成失败: {e}")
            return None
    
    def get_sql_from_mql(self, question: str) -> Dict[str, Any]:
        try:
            mql = self.get_mql_from_metrics(question)
            if not mql:
                return None
            url = 'http://115.159.125.146:9085/epoint-indexcenter-web/rest/indexapi/getgensql'
            headers = {
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                url=url,
                data=json.dumps(mql),
                headers=headers,
                verify=False,
                timeout=30
            )
            response.raise_for_status()
            result = response.json()
            print("生成的SQL:")
            print(json.dumps(result, indent=4, ensure_ascii=False))
            return result
        except Exception as e:
            print(f"SQL生成失败: {e}")
            return None

if __name__ == "__main__":
    api_test = StatisticsDemo()
    api_test.get_mql_from_metrics("2024年发布公告数量最多的创建人是谁？")
